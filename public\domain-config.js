// Domain Configuration for ICG Assessment Dashboard
// This file centralizes all domain-related configurations to ensure consistency
// and make future domain changes easier to manage

(function(global) {
    'use strict';

    // Current domain configuration
    const DOMAIN_CONFIG = {
        // Primary domains
        current: {
            dashboard: 'https://icg-dashboard.onrender.com',
            website: 'https://icg.co.uk',
            signup: 'https://icg-dashboard.onrender.com/signup.html'
        },
        
        // Legacy domains for backward compatibility
        legacy: {
            dashboard: 'https://dashboard.skillsassess.ai',
            website: 'https://skillsassess.ai',
            signup: 'https://dashboard.skillsassess.ai/signup.html'
        },

        // Assessment platform domains
        assessments: {
            digital: 'https://icg-digitalskills.onrender.com',
            ai: 'https://icg-aiskills.onrender.com',
            soft: 'https://icg-softskills.onrender.com'
        },

        // Email domains
        email: {
            current: 'icg.co.uk',
            legacy: 'skillsassess.ai'
        },

        // Admin credentials
        admin: {
            email: '<EMAIL>',
            legacyEmail: '<EMAIL>'
        }
    };

    // Utility functions for domain management
    const DomainUtils = {
        /**
         * Get the current signup URL with optional referral code
         * @param {string} referralCode - Optional referral code to append
         * @returns {string} Complete signup URL
         */
        getSignupUrl: function(referralCode) {
            const baseUrl = DOMAIN_CONFIG.current.signup;
            return referralCode ? `${baseUrl}?ref=${referralCode}` : baseUrl;
        },

        /**
         * Get referral link for sharing
         * @param {string} referralCode - Referral code to include
         * @returns {string} Complete referral link
         */
        getReferralLink: function(referralCode) {
            return this.getSignupUrl(referralCode);
        },

        /**
         * Check if a domain is a legacy domain
         * @param {string} domain - Domain to check
         * @returns {boolean} True if legacy domain
         */
        isLegacyDomain: function(domain) {
            return Object.values(DOMAIN_CONFIG.legacy).some(legacyDomain => 
                domain.includes(legacyDomain.replace('https://', ''))
            );
        },

        /**
         * Get current dashboard URL
         * @returns {string} Current dashboard URL
         */
        getDashboardUrl: function() {
            return DOMAIN_CONFIG.current.dashboard;
        },

        /**
         * Get terms and conditions URL
         * @returns {string} Terms URL
         */
        getTermsUrl: function() {
            return `${DOMAIN_CONFIG.current.website}/terms-and-conditions/`;
        },

        /**
         * Get privacy policy URL
         * @returns {string} Privacy policy URL
         */
        getPrivacyUrl: function() {
            return `${DOMAIN_CONFIG.current.website}/privacy-policy/`;
        },

        /**
         * Get assessment URL by type
         * @param {string} type - Assessment type (digital, ai, soft)
         * @returns {string} Assessment URL
         */
        getAssessmentUrl: function(type) {
            const assessmentDomain = DOMAIN_CONFIG.assessments[type];
            if (!assessmentDomain) {
                throw new Error(`Unknown assessment type: ${type}`);
            }
            
            // Return appropriate assessment page based on type
            switch(type) {
                case 'ai':
                    return `${assessmentDomain}/SGA_ai.html`;
                case 'digital':
                case 'soft':
                default:
                    return `${assessmentDomain}/SGA.html`;
            }
        },

        /**
         * Get current admin email
         * @returns {string} Admin email
         */
        getAdminEmail: function() {
            return DOMAIN_CONFIG.admin.email;
        },

        /**
         * Get notification recipients for the current domain
         * @returns {Array<string>} Array of email addresses
         */
        getNotificationRecipients: function() {
            const domain = DOMAIN_CONFIG.email.current;
            return [
                `harry@${domain}`,
                `liliana@${domain}`,
                `jo@${domain}`,
                `fabrizio@${domain}`,
                `robin@${domain}`
            ];
        }
    };

    // Expose configuration and utilities globally
    global.DOMAIN_CONFIG = DOMAIN_CONFIG;
    global.DomainUtils = DomainUtils;

    // For backward compatibility, also expose individual functions
    global.getSignupUrl = DomainUtils.getSignupUrl.bind(DomainUtils);
    global.getReferralLink = DomainUtils.getReferralLink.bind(DomainUtils);
    global.getDashboardUrl = DomainUtils.getDashboardUrl.bind(DomainUtils);

    console.log('Domain configuration loaded:', DOMAIN_CONFIG.current);

})(window);
