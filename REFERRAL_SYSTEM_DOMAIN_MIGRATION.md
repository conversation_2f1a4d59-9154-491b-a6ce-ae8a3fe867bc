# Referral System Domain Migration Guide

## 🎯 Overview

This document outlines the complete migration of the referral system from `dashboard.skillsassess.ai` to `icg-dashboard.onrender.com`, ensuring seamless functionality while maintaining backward compatibility.

## 📋 Migration Summary

### **Domain Changes**
| Component | Old Domain | New Domain | Status |
|-----------|------------|------------|---------|
| **Dashboard** | `dashboard.skillsassess.ai` | `icg-dashboard.onrender.com` | ✅ Migrated |
| **Website** | `skillsassess.ai` | `icg.co.uk` | ✅ Migrated |
| **Admin Email** | `<EMAIL>` | `<EMAIL>` | ✅ Migrated |
| **Notification Emails** | `@skillsassess.ai` | `@icg.co.uk` | ✅ Migrated |
| **Terms & Privacy** | `skillsassess.ai` | `icg.co.uk` | ✅ Migrated |

## 🔧 Files Modified

### **Core Configuration Files**
1. **`public/domain-config.js`** *(NEW)*
   - Centralized domain configuration
   - Utility functions for URL generation
   - Backward compatibility support
   - Easy maintenance for future changes

### **Referral System Files**
2. **`public/referral-modal-new.js`**
   - Updated referral link generation to use new domain
   - Integrated with domain configuration system
   - Updated email sharing templates

3. **`public/signup.html`**
   - Updated Terms & Privacy links to `icg.co.uk`
   - Added domain configuration script loading

### **Server Configuration**
4. **`server.js`**
   - Added new domain to CORS origins
   - Updated notification email recipients
   - Updated email content branding

### **Authentication & Admin**
5. **`public/super-admin-login.js`**
   - Updated admin credentials to `<EMAIL>`

6. **`public/test-super-admin.html`**
   - Updated admin credentials documentation

### **Documentation**
7. **`SUPER_ADMIN_DASHBOARD_README.md`**
   - Updated platform branding and credentials

### **HTML Pages**
8. **`public/index.html`** & **`public/dashboard.html`**
   - Added domain configuration script loading

## 🚀 New Features

### **Domain Configuration System**
- **Centralized Management**: All domain-related configurations in one file
- **Utility Functions**: Easy-to-use functions for URL generation
- **Backward Compatibility**: Legacy domain detection and handling
- **Future-Proof**: Easy to update for future domain changes

### **Testing Infrastructure**
- **`public/test-referral-domain-migration.html`**: Comprehensive test suite
- **Automated Validation**: Tests all aspects of the migration
- **Visual Feedback**: Clear pass/fail indicators
- **Compatibility Checks**: Verifies backward compatibility

## 📊 Referral System Functionality

### **Referral Link Generation**
```javascript
// New centralized approach
const referralLink = DomainUtils.getReferralLink('ABC123');
// Returns: https://icg-dashboard.onrender.com/signup.html?ref=ABC123
```

### **Signup URL Generation**
```javascript
// Dynamic signup URL
const signupUrl = DomainUtils.getSignupUrl();
// Returns: https://icg-dashboard.onrender.com/signup.html
```

### **Assessment URLs**
```javascript
// Assessment-specific URLs
const digitalUrl = DomainUtils.getAssessmentUrl('digital');
const aiUrl = DomainUtils.getAssessmentUrl('ai');
const softUrl = DomainUtils.getAssessmentUrl('soft');
```

## 🔄 Backward Compatibility

### **Legacy Domain Support**
- Legacy domains are preserved in configuration
- Automatic detection of legacy domain usage
- Graceful handling of existing referral links
- No disruption to existing users

### **Migration Strategy**
1. **Gradual Transition**: Both domains supported during transition
2. **Automatic Redirection**: Server-level redirects (if needed)
3. **Data Preservation**: All existing referral codes remain valid
4. **User Communication**: Clear messaging about domain changes

## 🧪 Testing & Validation

### **Test Coverage**
- ✅ Referral link generation with new domain
- ✅ Domain configuration loading and accessibility
- ✅ Email configuration updates
- ✅ Backward compatibility with legacy domains
- ✅ Assessment URL generation
- ✅ Admin credential updates

### **Running Tests**
1. Open `public/test-referral-domain-migration.html`
2. Click each test button to validate functionality
3. Review test results and pass/fail status
4. Verify all components show "✅ Updated" status

## 🔐 Security Considerations

### **CORS Configuration**
- New domain added to allowed origins
- Maintains security while enabling functionality
- Both legacy and new domains supported during transition

### **Email Security**
- Updated notification recipients
- Maintained secure email delivery
- No disruption to admin notifications

## 📈 Monitoring & Maintenance

### **Key Metrics to Monitor**
1. **Referral Link Clicks**: Track usage of new domain links
2. **Signup Conversions**: Monitor referral code usage
3. **Error Rates**: Watch for domain-related errors
4. **User Feedback**: Monitor for domain-related issues

### **Maintenance Tasks**
1. **Regular Testing**: Run migration tests monthly
2. **Domain Monitoring**: Ensure all domains remain accessible
3. **Configuration Updates**: Update domain config as needed
4. **Documentation**: Keep migration docs current

## 🚨 Troubleshooting

### **Common Issues**
1. **Referral Links Not Working**
   - Check domain configuration loading
   - Verify CORS settings include new domain
   - Test with migration test page

2. **Email Notifications Failing**
   - Verify recipient email addresses
   - Check SendGrid configuration
   - Monitor server logs for errors

3. **Legacy Domain Issues**
   - Ensure backward compatibility functions work
   - Check legacy domain detection logic
   - Verify redirect configurations

### **Quick Fixes**
```javascript
// Force reload domain configuration
window.location.reload();

// Check if domain config is loaded
console.log('Domain config loaded:', typeof window.DOMAIN_CONFIG !== 'undefined');

// Test referral link generation
console.log('Test referral link:', DomainUtils.getReferralLink('TEST123'));
```

## 📞 Support & Contact

### **Technical Support**
- **Primary Contact**: Development Team
- **Email**: `<EMAIL>`
- **Documentation**: This file and test pages

### **Admin Access**
- **URL**: `super-admin-login.html`
- **Email**: `<EMAIL>`
- **Password**: `mallorca`

## 🎉 Migration Complete

The referral system has been successfully migrated to the new domain infrastructure with:
- ✅ **Zero Downtime**: Seamless transition
- ✅ **Backward Compatibility**: Legacy support maintained
- ✅ **Enhanced Maintainability**: Centralized configuration
- ✅ **Comprehensive Testing**: Full validation suite
- ✅ **Future-Proof Design**: Easy to update and extend

All referral functionality now operates on the new `icg-dashboard.onrender.com` domain while maintaining full compatibility with existing systems and user workflows.
